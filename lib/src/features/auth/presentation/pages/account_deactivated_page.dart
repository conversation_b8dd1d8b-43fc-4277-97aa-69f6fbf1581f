import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/sign_in_page.dart';

/// Page shown after successful account deactivation
class AccountDeactivatedPage extends StatelessWidget {
  static const String routeName = '/account-deactivated';

  const AccountDeactivatedPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 500),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.destructive.withValues(
                        alpha: 0.1,
                      ),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.colorScheme.destructive.withValues(
                          alpha: 0.2,
                        ),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      LucideIcons.userX,
                      size: 60,
                      color: theme.colorScheme.destructive,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Title
                  Text(
                    'Account Deactivated',
                    style: theme.textTheme.h1.copyWith(
                      color: theme.colorScheme.destructive,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  // Description
                  Text(
                    'Your account has been successfully deactivated. You no longer have access to the 3Pay Global platform.',
                    style: theme.textTheme.p.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 32),

                  // Contact Information Card
                  ShadCard(
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                LucideIcons.info,
                                size: 20,
                                color: theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 8),
                              Flexible(
                                child: Text(
                                  'Deactivated by mistake?',
                                  style: theme.textTheme.h4.copyWith(
                                    fontWeight: FontWeight.w100,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'If you believe this was done in error or you need to reactivate your account, please contact our support team.',
                            style: theme.textTheme.p.copyWith(
                              color: theme.colorScheme.mutedForeground,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Email Contact
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.muted.withValues(
                                alpha: 0.3,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: theme.colorScheme.border,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  LucideIcons.mail,
                                  size: 18,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Email Support',
                                        style: theme.textTheme.small.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: theme.colorScheme.foreground,
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      GestureDetector(
                                        onTap:
                                            () => _copyToClipboard(
                                              context,
                                              '<EMAIL>',
                                            ),
                                        child: Text(
                                          '<EMAIL>',
                                          style: theme.textTheme.p.copyWith(
                                            color: theme.colorScheme.primary,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                ShadButton.ghost(
                                  onPressed:
                                      () => _copyToClipboard(
                                        context,
                                        '<EMAIL>',
                                      ),
                                  child: const Icon(LucideIcons.copy, size: 16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Action Buttons
                  Column(
                    children: [
                      ShadButton(
                        onPressed:
                            () => Navigator.of(context).pushNamedAndRemoveUntil(
                              SignInPage.routeName,
                              (route) => false,
                            ),
                        width: double.infinity,
                        child: const Text('Return to Sign In'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Footer
                  Text(
                    '© 2024 3Pay Global. All rights reserved.',
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ShadToaster.of(context).show(
      const ShadToast(
        title: Text('Copied to Clipboard'),
        description: Text('Email address has been copied to your clipboard.'),
      ),
    );
  }
}
