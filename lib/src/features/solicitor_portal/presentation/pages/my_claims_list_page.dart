import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/claim_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/edit_funding_application_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/application_chat_page.dart';
import 'package:intl/intl.dart';

enum SortOption { dateUpdated, dateCreated, title, status }

enum ViewMode { card, compact }

class MyClaimsListPage extends StatefulWidget {
  static const String routeName = '/solicitor/my-claims';

  final List<String>? statusFilter;
  final String? pageTitle;

  const MyClaimsListPage({super.key, this.statusFilter, this.pageTitle});

  @override
  State<MyClaimsListPage> createState() => _MyClaimsListPageState();
}

class _MyClaimsListPageState extends State<MyClaimsListPage>
    with TickerProviderStateMixin {
  final PocketBaseService _pocketBaseService = PocketBaseService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<FundingApplicationData> _applications = [];
  List<FundingApplicationData> _filteredApplications = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  SortOption _sortOption = SortOption.dateUpdated;
  ViewMode _viewMode = ViewMode.card;
  Set<String> _selectedStatuses = {};
  late AnimationController _fabAnimationController;
  bool _showFab = false;

  // Status options for filtering
  final List<String> _availableStatuses = [
    'draft',
    'pending',
    'under_review',
    'approved',
    'rejected',
    'completed',
  ];

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
    _fetchApplications();
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 100 && !_showFab) {
      setState(() => _showFab = true);
      _fabAnimationController.forward();
    } else if (_scrollController.offset <= 100 && _showFab) {
      setState(() => _showFab = false);
      _fabAnimationController.reverse();
    }
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterAndSortApplications();
    });
  }

  Future<void> _fetchApplications() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final String currentUserId =
          _pocketBaseService.client.authStore.record?.id ?? '';

      final solicitorProfilesResult = await _pocketBaseService.client
          .collection('solicitor_profiles')
          .getList(filter: 'user_id = "$currentUserId"', page: 1, perPage: 1);

      if (solicitorProfilesResult.items.isEmpty) {
        setState(() {
          _error = 'No solicitor profile found for current user';
          _isLoading = false;
        });
        return;
      }

      final solicitorProfileId = solicitorProfilesResult.items[0].id;

      List<String> filterParts = [
        'solicitor_profile_id ~ "$solicitorProfileId"',
      ];

      if (widget.statusFilter != null && widget.statusFilter!.isNotEmpty) {
        final statusConditions = widget.statusFilter!
            .map((status) => 'application_status = "$status"')
            .join(' || ');
        filterParts.add('($statusConditions)');
      }

      final filterString = filterParts.join(' && ');

      final records = await _pocketBaseService.getFullList(
        collectionName: 'funding_applications',
        filter: filterString,
        sort: '-updated',
      );

      _applications =
          records
              .map(
                (record) => FundingApplicationData.fromJson(
                  record.id,
                  record.data,
                  recordUpdatedTimestamp: record.get<String>('updated'),
                ),
              )
              .toList();

      _filterAndSortApplications();
    } catch (e) {
      setState(() {
        _error = 'Failed to load funding applications: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterAndSortApplications() {
    List<FundingApplicationData> filtered =
        _applications.where((app) {
          // Search filter
          final matchesSearch =
              _searchQuery.isEmpty ||
              (app.claimTitle?.toLowerCase().contains(_searchQuery) ?? false) ||
              (app.id?.toLowerCase().contains(_searchQuery) ?? false);

          // Status filter
          final matchesStatus =
              _selectedStatuses.isEmpty ||
              _selectedStatuses.contains(app.applicationStatus?.toLowerCase());

          return matchesSearch && matchesStatus;
        }).toList();

    // Sort applications
    switch (_sortOption) {
      case SortOption.dateUpdated:
        filtered.sort((a, b) {
          final aDate =
              DateTime.tryParse(a.lastUpdated ?? '') ?? DateTime(1970);
          final bDate =
              DateTime.tryParse(b.lastUpdated ?? '') ?? DateTime(1970);
          return bDate.compareTo(aDate);
        });
        break;
      case SortOption.title:
        filtered.sort(
          (a, b) => (a.claimTitle ?? '').compareTo(b.claimTitle ?? ''),
        );
        break;
      case SortOption.status:
        filtered.sort(
          (a, b) =>
              (a.applicationStatus ?? '').compareTo(b.applicationStatus ?? ''),
        );
        break;
      case SortOption.dateCreated:
        // Assuming you have a created date field
        filtered.sort(
          (a, b) => (b.id ?? '').compareTo(a.id ?? ''),
        ); // Fallback to ID sort
        break;
    }

    setState(() {
      _filteredApplications = filtered;
    });
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return const Color(0xFF10B981); // Emerald
      case 'pending':
        return const Color(0xFFF59E0B); // Amber
      case 'rejected':
        return const Color(0xFFEF4444); // Red
      case 'draft':
        return const Color(0xFF6B7280); // Gray
      case 'under_review':
        return const Color(0xFF3B82F6); // Blue
      case 'completed':
        return const Color(0xFF8B5CF6); // Purple
      default:
        return const Color(0xFF6B7280);
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return LucideIcons.circleCheck;
      case 'pending':
        return LucideIcons.clock;
      case 'rejected':
        return LucideIcons.ban;
      case 'draft':
        return LucideIcons.fileText;
      case 'under_review':
        return LucideIcons.searchCheck;
      case 'completed':
        return LucideIcons.checkCheck;
      default:
        return LucideIcons.info;
    }
  }

  String _formatStatus(String? status) {
    if (status == null) return 'Unknown';
    return status
        .replaceAll('_', ' ')
        .split(' ')
        .map(
          (word) =>
              word.isNotEmpty
                  ? word[0].toUpperCase() + word.substring(1)
                  : word,
        )
        .join(' ');
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          ShadInput(
            controller: _searchController,
            placeholder: const Text('Search claims by title or ID...'),
            leading: const Icon(LucideIcons.search, size: 18),
            trailing:
                _searchQuery.isNotEmpty
                    ? IconButton(
                      icon: const Icon(LucideIcons.x, size: 18),
                      onPressed: () {
                        _searchController.clear();
                      },
                    )
                    : null,
          ),

          const SizedBox(height: 12),

          // Filters and controls row
          Row(
            children: [
              // Status filter
              Expanded(
                child: ShadButton.outline(
                  size: ShadButtonSize.sm,
                  onPressed: _showStatusFilterDialog,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(LucideIcons.listFilter, size: 16),
                      const SizedBox(width: 6),
                      Text(
                        _selectedStatuses.isEmpty
                            ? 'All Statuses'
                            : '${_selectedStatuses.length} Selected',
                      ),
                      if (_selectedStatuses.isNotEmpty) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            LucideIcons.x,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // Sort dropdown
              ShadButton.outline(
                size: ShadButtonSize.sm,
                onPressed: _showSortDialog,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(LucideIcons.arrowUpDown, size: 16),
                    const SizedBox(width: 6),
                    Text(_getSortLabel()),
                  ],
                ),
              ),

              const SizedBox(width: 8),

              // View mode toggle
              ShadButton.outline(
                size: ShadButtonSize.sm,
                onPressed: () {
                  setState(() {
                    _viewMode =
                        _viewMode == ViewMode.card
                            ? ViewMode.compact
                            : ViewMode.card;
                  });
                },
                child: Icon(
                  _viewMode == ViewMode.card
                      ? LucideIcons.list
                      : LucideIcons.layoutGrid,
                  size: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getSortLabel() {
    switch (_sortOption) {
      case SortOption.dateUpdated:
        return 'Updated';
      case SortOption.dateCreated:
        return 'Created';
      case SortOption.title:
        return 'Title';
      case SortOption.status:
        return 'Status';
    }
  }

  void _showStatusFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setDialogState) => AlertDialog(
                  title: const Text('Filter by Status'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children:
                        _availableStatuses.map((status) {
                          final isSelected = _selectedStatuses.contains(status);
                          return CheckboxListTile(
                            title: Text(_formatStatus(status)),
                            value: isSelected,
                            onChanged: (value) {
                              setDialogState(() {
                                if (value == true) {
                                  _selectedStatuses.add(status);
                                } else {
                                  _selectedStatuses.remove(status);
                                }
                              });
                            },
                          );
                        }).toList(),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _selectedStatuses.clear();
                          _filterAndSortApplications();
                        });
                        Navigator.pop(context);
                      },
                      child: const Text('Clear All'),
                    ),
                    ShadButton(
                      onPressed: () {
                        setState(() {
                          _filterAndSortApplications();
                        });
                        Navigator.pop(context);
                      },
                      child: const Text('Apply'),
                    ),
                  ],
                ),
          ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sort by'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  SortOption.values.map((option) {
                    return RadioListTile<SortOption>(
                      title: Text(_getSortOptionLabel(option)),
                      value: option,
                      groupValue: _sortOption,
                      onChanged: (value) {
                        setState(() {
                          _sortOption = value!;
                          _filterAndSortApplications();
                        });
                        Navigator.pop(context);
                      },
                    );
                  }).toList(),
            ),
          ),
    );
  }

  String _getSortOptionLabel(SortOption option) {
    switch (option) {
      case SortOption.dateUpdated:
        return 'Last Updated';
      case SortOption.dateCreated:
        return 'Date Created';
      case SortOption.title:
        return 'Claim Title';
      case SortOption.status:
        return 'Status';
    }
  }

  Widget _buildStatsBar() {
    final statusCounts = <String, int>{};
    for (final app in _applications) {
      final status = app.applicationStatus?.toLowerCase() ?? 'unknown';
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        children: [
          Text(
            '${_filteredApplications.length} of ${_applications.length} claims',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const Spacer(),
          if (statusCounts.isNotEmpty) ...[
            ...statusCounts.entries
                .take(3)
                .map(
                  (entry) => Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: _getStatusColor(entry.key),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${entry.value}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
          ],
        ],
      ),
    );
  }

  Widget _buildApplicationCard(FundingApplicationData application) {
    final statusColor = _getStatusColor(application.applicationStatus);
    final statusIcon = _getStatusIcon(application.applicationStatus);
    final formattedStatus = _formatStatus(application.applicationStatus);

    if (_viewMode == ViewMode.compact) {
      return _buildCompactCard(
        application,
        statusColor,
        statusIcon,
        formattedStatus,
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 6.0),
      child: ShadCard(
        child: InkWell(
          onTap:
              () => Navigator.pushNamed(
                context,
                ClaimDetailPage.routeName,
                arguments: application.id,
              ),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and status
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            application.claimTitle ?? 'Untitled Application',
                            style: ShadTheme.of(context).textTheme.h4?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  _getDisplayId(application.id),
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 12,
                                    fontFamily: 'monospace',
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 8),
                              GestureDetector(
                                onTap: () {
                                  Clipboard.setData(
                                    ClipboardData(text: application.id ?? ''),
                                  );
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('ID copied to clipboard'),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                                child: Icon(
                                  LucideIcons.copy,
                                  size: 12,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: statusColor.withOpacity(0.3)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(statusIcon, size: 14, color: statusColor),
                          const SizedBox(width: 6),
                          Flexible(
                            child: Text(
                              formattedStatus,
                              style: TextStyle(
                                color: statusColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Additional info section
                Wrap(
                  spacing: 12,
                  runSpacing: 8,
                  children: [
                    if (application.stageOfClaim != null)
                      _buildInfoChip(
                        LucideIcons.layers,
                        _formatStatus(application.stageOfClaim),
                        Colors.blue,
                      ),

                    if (application.lastUpdated != null &&
                        application.lastUpdated!.isNotEmpty)
                      Builder(
                        builder: (context) {
                          final dateTime = DateTime.tryParse(
                            application.lastUpdated!,
                          );
                          if (dateTime != null) {
                            return _buildInfoChip(
                              LucideIcons.clock,
                              _getTimeAgo(dateTime),
                              Colors.grey,
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                  ],
                ),

                const SizedBox(height: 20),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: ShadButton.outline(
                        size: ShadButtonSize.sm,
                        onPressed:
                            () => Navigator.pushNamed(
                              context,
                              EditFundingApplicationPage.routeName,
                              arguments: application.id,
                            ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(LucideIcons.pencil, size: 14),
                            SizedBox(width: 6),
                            Text('Edit'),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ShadButton.outline(
                        size: ShadButtonSize.sm,
                        onPressed:
                            () => Navigator.pushNamed(
                              context,
                              ApplicationChatPage.routeName,
                              arguments: application.id,
                            ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(LucideIcons.messageSquare, size: 14),
                            SizedBox(width: 6),
                            Text('Chat'),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.sm,
                        onPressed:
                            () => Navigator.pushNamed(
                              context,
                              ClaimDetailPage.routeName,
                              arguments: application.id,
                            ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(LucideIcons.eye, size: 14),
                            SizedBox(width: 6),
                            Text('View'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactCard(
    FundingApplicationData application,
    Color statusColor,
    IconData statusIcon,
    String formattedStatus,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
      child: ShadCard(
        child: InkWell(
          onTap:
              () => Navigator.pushNamed(
                context,
                ClaimDetailPage.routeName,
                arguments: application.id,
              ),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(statusIcon, color: statusColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        application.claimTitle ?? 'Untitled Application',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        formattedStatus,
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (application.lastUpdated != null &&
                    application.lastUpdated!.isNotEmpty)
                  Builder(
                    builder: (context) {
                      final dateTime = DateTime.tryParse(
                        application.lastUpdated!,
                      );
                      if (dateTime != null) {
                        return Text(
                          _getTimeAgo(dateTime),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 11,
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                const SizedBox(width: 8),
                const Icon(LucideIcons.chevronRight, size: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, MaterialColor color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color.shade700),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                color: color.shade700,
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  String _getDisplayId(String? id) {
    if (id == null || id.isEmpty) return 'ID: N/A';
    return id.length > 8 ? 'ID: ${id.substring(0, 8)}...' : 'ID: $id';
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return DateFormat('MMM d').format(dateTime);
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildApplicationList() {
    if (_filteredApplications.isEmpty && _applications.isNotEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  LucideIcons.searchX,
                  size: 48,
                  color: Colors.blue.shade400,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'No claims match your filters',
                style: ShadTheme.of(
                  context,
                ).textTheme.h4?.copyWith(color: Colors.grey.shade700),
              ),
              const SizedBox(height: 8),
              Text(
                'Try adjusting your search or filter criteria.',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ShadButton.outline(
                onPressed: () {
                  _searchController.clear();
                  setState(() {
                    _selectedStatuses.clear();
                    _filterAndSortApplications();
                  });
                },
                child: const Text('Clear Filters'),
              ),
            ],
          ),
        ),
      );
    }

    if (_applications.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  LucideIcons.folderSearch,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'No funding applications found',
                style: ShadTheme.of(
                  context,
                ).textTheme.h4?.copyWith(color: Colors.grey.shade700),
              ),
              const SizedBox(height: 8),
              Text(
                'Your funding applications will appear here once created.',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _fetchApplications,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.only(bottom: 80),
        itemCount: _filteredApplications.length,
        itemBuilder: (context, index) {
          return _buildApplicationCard(_filteredApplications[index]);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          widget.pageTitle ?? 'My Claims',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 1,
        actions: [
          if (!_isLoading)
            IconButton(
              icon: const Icon(LucideIcons.refreshCw),
              onPressed: _fetchApplications,
              tooltip: 'Refresh',
            ),
        ],
      ),
      body:
          _isLoading
              ? const LoadingSpinnerWidget()
              : _error != null
              ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          LucideIcons.circleAlert,
                          size: 48,
                          color: Colors.red.shade400,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Something went wrong',
                        style: ShadTheme.of(
                          context,
                        ).textTheme.h4?.copyWith(color: Colors.red.shade700),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        style: TextStyle(
                          color: Colors.red.shade600,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ShadButton.outline(
                        onPressed: _fetchApplications,
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(LucideIcons.refreshCw, size: 16),
                            SizedBox(width: 8),
                            Text('Try Again'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
              : Column(
                children: [
                  _buildSearchAndFilters(),
                  _buildStatsBar(),
                  Expanded(child: _buildApplicationList()),
                ],
              ),
      floatingActionButton: AnimatedBuilder(
        animation: _fabAnimationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _fabAnimationController.value,
            child: FloatingActionButton(
              onPressed: () {
                _scrollController.animateTo(
                  0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
              child: const Icon(LucideIcons.arrowUp),
            ),
          );
        },
      ),
    );
  }
}
